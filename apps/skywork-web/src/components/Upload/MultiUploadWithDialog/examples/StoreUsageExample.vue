<template>
  <div class="store-usage-example">
    <h3>MultiUploadDialog Store 使用示例</h3>
    
    <!-- 显示当前选中的知识库列表 -->
    <div class="selected-list">
      <h4>当前选中的知识库列表 ({{ selectedList.length }} 项):</h4>
      <div v-if="selectedList.length === 0" class="empty-state">
        暂无选中项
      </div>
      <div v-else>
        <div 
          v-for="item in selectedList" 
          :key="item.id"
          class="selected-item"
        >
          <span class="item-name">{{ item.name }}</span>
          <span class="item-id">(ID: {{ item.id }})</span>
          <button @click="removeItem(item.id)" class="remove-btn">移除</button>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <button @click="clearAll" class="clear-btn">清空所有</button>
      <button @click="addTestItem" class="add-btn">添加测试项</button>
      <button @click="logStoreData" class="log-btn">打印 Store 数据</button>
    </div>

    <!-- 显示其他存储的数据 -->
    <div class="other-data">
      <h4>其他存储数据:</h4>
      <div>
        <label>测试数据: </label>
        <input 
          v-model="testData" 
          @input="updateTestData"
          placeholder="输入测试数据"
        />
      </div>
      <div>
        <label>当前测试数据值: </label>
        <span>{{ currentTestData }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { inject, computed, ref } from 'vue';
import type { KnowledgeSelectedItem } from '../store/useMultiUploadDialogStore';

// 注入弹窗 store
const dialogStore = inject('multiUploadDialogStore') as any;

// 响应式数据
const testData = ref('');

// 计算属性
const selectedList = computed(() => {
  return dialogStore?.selectedList.value || [];
});

const currentTestData = computed(() => {
  return dialogStore?.getData('testData') || '无数据';
});

// 方法
const removeItem = (id: string) => {
  if (dialogStore) {
    dialogStore.removeSelectedItem(id);
  }
};

const clearAll = () => {
  if (dialogStore) {
    dialogStore.clearSelectedList();
  }
};

const addTestItem = () => {
  if (dialogStore) {
    const testItem: KnowledgeSelectedItem = {
      id: `test-${Date.now()}`,
      name: `测试项目 ${Date.now()}`,
      checked: true,
      type: 'test',
      createdAt: new Date().toISOString()
    };
    dialogStore.addSelectedItem(testItem);
  }
};

const updateTestData = () => {
  if (dialogStore) {
    dialogStore.setData('testData', testData.value);
  }
};

const logStoreData = () => {
  if (dialogStore) {
    console.log('完整 Store 数据:', dialogStore.dialogData.value);
    console.log('选中列表:', dialogStore.selectedList.value);
    console.log('测试数据:', dialogStore.getData('testData'));
  }
};
</script>

<style scoped>
.store-usage-example {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin: 20px;
}

.selected-list {
  margin-bottom: 20px;
}

.selected-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 5px;
}

.item-name {
  font-weight: bold;
}

.item-id {
  color: #666;
  font-size: 12px;
}

.remove-btn {
  background: #ff4757;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
}

.actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.clear-btn {
  background: #ff6b6b;
  color: white;
}

.add-btn {
  background: #51cf66;
  color: white;
}

.log-btn {
  background: #339af0;
  color: white;
}

.other-data {
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.other-data > div {
  margin-bottom: 10px;
}

.other-data label {
  display: inline-block;
  width: 120px;
  font-weight: bold;
}

.other-data input {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.empty-state {
  color: #999;
  font-style: italic;
}
</style>
