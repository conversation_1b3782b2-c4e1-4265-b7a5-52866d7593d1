# MultiUploadWithDialog Store 使用说明

## 概述

`useMultiUploadDialogStore` 是为 `MultiUploadWithDialog` 组件设计的状态管理 store，用于存储弹窗内的数据，支持 localStorage 持久化。

## 功能特性

- ✅ 支持知识库选中列表的存储和管理
- ✅ 自动 localStorage 持久化
- ✅ 支持扩展存储任意数据
- ✅ 弹窗关闭时自动清理数据
- ✅ 类型安全的 TypeScript 支持

## 基本使用

### 1. 在父组件中提供 store

```vue
<script setup>
import { provide } from 'vue';
import { useMultiUploadDialogStore } from './store/useMultiUploadDialogStore';

const dialogStore = useMultiUploadDialogStore();
provide('multiUploadDialogStore', dialogStore);
</script>
```

### 2. 在子组件中使用 store

```vue
<script setup>
import { inject } from 'vue';
import type { KnowledgeSelectedItem } from '@/components/Upload/MultiUploadWithDialog/store/useMultiUploadDialogStore';

const dialogStore = inject('multiUploadDialogStore');

// 保存选中的知识库列表
const handleSaveSelection = (selectedItems) => {
  const formattedList: KnowledgeSelectedItem[] = selectedItems.map(item => ({
    id: item.space?.file_id || item.id,
    name: item.space?.file_name || item.name,
    checked: item.checked,
    ...item
  }));
  
  dialogStore.setSelectedList(formattedList);
};

// 获取选中的列表
const getSelectedList = () => {
  return dialogStore.selectedList.value;
};
</script>
```

## API 参考

### 状态

- `dialogData`: 完整的弹窗数据对象
- `selectedList`: 知识库选中列表（可读写的计算属性）

### 方法

#### 知识库选中列表管理
- `setSelectedList(list)`: 设置选中列表
- `addSelectedItem(item)`: 添加选中项
- `removeSelectedItem(id)`: 移除选中项
- `clearSelectedList()`: 清空选中列表

#### 通用数据管理
- `setData(key, value)`: 设置任意数据
- `getData(key)`: 获取任意数据
- `clearData()`: 清理所有数据

#### 存储管理
- `loadFromStorage()`: 从 localStorage 加载数据
- `saveToStorage()`: 保存数据到 localStorage

## 数据结构

### KnowledgeSelectedItem

```typescript
interface KnowledgeSelectedItem {
  id: string;
  name: string;
  checked: boolean;
  [key: string]: any; // 支持扩展字段
}
```

### MultiUploadDialogData

```typescript
interface MultiUploadDialogData {
  selectedList: KnowledgeSelectedItem[];
  [key: string]: any; // 支持扩展字段
}
```

## 注意事项

1. **自动持久化**: 数据会自动保存到 localStorage，键名为 `multi-upload-dialog-data`
2. **自动清理**: 弹窗关闭时会自动调用 `clearData()` 清理数据
3. **错误处理**: localStorage 操作失败时会在控制台输出警告，不会影响正常功能
4. **类型安全**: 提供完整的 TypeScript 类型定义

## 扩展使用

如果需要存储其他类型的数据，可以使用通用的 `setData` 和 `getData` 方法：

```javascript
// 存储其他数据
dialogStore.setData('uploadProgress', { current: 5, total: 10 });
dialogStore.setData('userPreferences', { theme: 'dark' });

// 获取数据
const progress = dialogStore.getData('uploadProgress');
const preferences = dialogStore.getData('userPreferences');
```
