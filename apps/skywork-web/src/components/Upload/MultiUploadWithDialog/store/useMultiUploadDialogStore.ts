import { defineStore } from "pinia";
import { ref } from "vue";

export interface KnowledgeSelectedItem {
  id: string;
  name: string;
  checked: boolean;
  [key: string]: any;
}

export const useMultiUploadDialogStore = defineStore("multiUploadDialog", () => {
  const selectedList = ref<KnowledgeSelectedItem[]>([]);

  const setSelectedList = (list: KnowledgeSelectedItem[]) => {
    selectedList.value = list;
  };

  return {
    selectedList,
    setSelectedList,
  };
});
