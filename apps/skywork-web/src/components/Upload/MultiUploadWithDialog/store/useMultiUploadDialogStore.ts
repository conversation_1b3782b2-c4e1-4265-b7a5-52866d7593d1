import { defineStore } from "pinia";
import { computed, ref } from "vue";

// 知识库选中项的类型定义
export interface KnowledgeSelectedItem {
  id: string;
  name: string;
  checked: boolean;
  [key: string]: any;
}

// 弹窗数据类型定义
export interface MultiUploadDialogData {
  selectedList: KnowledgeSelectedItem[];
  [key: string]: any;
}

const STORAGE_KEY = "multi-upload-dialog-data";

export const useMultiUploadDialogStore = defineStore("multiUploadDialog", () => {
  // 弹窗内的数据状态
  const dialogData = ref<MultiUploadDialogData>({
    selectedList: [],
  });

  // 从 localStorage 加载数据
  const loadFromStorage = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedData = JSON.parse(stored);
        dialogData.value = { ...dialogData.value, ...parsedData };
      }
    } catch (error) {
      console.warn("Failed to load dialog data from localStorage:", error);
    }
  };

  // 保存数据到 localStorage
  const saveToStorage = () => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(dialogData.value));
    } catch (error) {
      console.warn("Failed to save dialog data to localStorage:", error);
    }
  };

  // 计算属性：获取选中的知识库列表
  const selectedList = computed({
    get: () => dialogData.value.selectedList,
    set: (value: KnowledgeSelectedItem[]) => {
      dialogData.value.selectedList = value;
      saveToStorage();
    },
  });

  // 设置选中的知识库列表
  const setSelectedList = (list: KnowledgeSelectedItem[]) => {
    selectedList.value = list;
  };

  // 添加选中项
  const addSelectedItem = (item: KnowledgeSelectedItem) => {
    const exists = selectedList.value.find((selected) => selected.id === item.id);
    if (!exists) {
      selectedList.value = [...selectedList.value, item];
    }
  };

  // 移除选中项
  const removeSelectedItem = (id: string) => {
    selectedList.value = selectedList.value.filter((item) => item.id !== id);
  };

  // 清空选中列表
  const clearSelectedList = () => {
    selectedList.value = [];
  };

  // 设置任意数据
  const setData = (key: string, value: any) => {
    dialogData.value[key] = value;
    saveToStorage();
  };

  // 获取任意数据
  const getData = (key: string) => {
    return dialogData.value[key];
  };

  // 清理所有数据
  const clearData = () => {
    dialogData.value = {
      selectedList: [],
    };
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn("Failed to clear dialog data from localStorage:", error);
    }
  };

  // 初始化时加载数据
  loadFromStorage();

  return {
    // 状态
    dialogData: computed(() => dialogData.value),
    selectedList,

    // 方法
    setSelectedList,
    addSelectedItem,
    removeSelectedItem,
    clearSelectedList,
    setData,
    getData,
    clearData,
    loadFromStorage,
    saveToStorage,
  };
});
