<script lang="ts" setup>
import { computed, ref, provide } from "vue";
import { UploadFrom } from "../types";
import Title from "./components/Title.vue";
import { ElDialog } from "element-plus";
import MultiUpload from "../MultiUpload/index.vue";
import { useMultiUploadDialogStore } from "@/components/Upload/MultiUploadWithDialog/store/useMultiUploadDialogStore";

interface Props {
  visible: boolean;
  from: UploadFrom;
}

const props = defineProps<Props>();
const emit = defineEmits(["update:visible"]);

const multiUploadRef = ref();

// 创建弹窗 store 实例
const dialogStore = useMultiUploadDialogStore();

// 提供 store 给子组件使用
provide("multiUploadDialogStore", dialogStore);

// 包装成一个可写 computed，解决 eslint 报错
const visibleProxy = computed({
  get: () => props.visible,
  set: (val: boolean) => {
    emit("update:visible", val);
    // 弹窗关闭时清理数据
    if (!val) {
      dialogStore.setSelectedList([]);
    }
  },
});

const currentComponent = computed(() => {
  return multiUploadRef.value?.currentComponent || "home";
});

const handleSetCurrentComponent = (component: string) => {
  multiUploadRef.value?.setCurrentComponent(component);
};
</script>

<template>
  <ElDialog class="p-[24px] pb-[30px]" v-model="visibleProxy" :width="800" :append-to-body="true" :show-close="false">
    <template #header>
      <Title
        :currentComponent="currentComponent"
        :handleClose="() => (visibleProxy = false)"
        :handleSetCurrentComponent="handleSetCurrentComponent"
      />
    </template>
    <MultiUpload ref="multiUploadRef" :module-name="props.from" :upload-from="props.from" />
  </ElDialog>
</template>
