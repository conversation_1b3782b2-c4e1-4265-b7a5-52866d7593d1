<script lang="tsx" setup>
import { ref } from "vue";

import { COMPONENT_MAP } from "./constants";

const currentComponent = ref("knowledge");
const setCurrentComponent = (component: string) => {
  currentComponent.value = component;
};

defineExpose({
  currentComponent,
  setCurrentComponent,
});
</script>

<template>
  <div class="h-full">
    <component
      :is="COMPONENT_MAP[currentComponent].components"
      :currentComponent="currentComponent"
      :setCurrentComponent="setCurrentComponent"
    />
  </div>
</template>
