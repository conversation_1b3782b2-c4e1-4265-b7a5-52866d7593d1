<script lang="ts" setup>
import { defineProps, computed, h } from "vue";
import {
  ElSpace,
  ElDivider,
  ElDropdown,
  ElDropdownMenu,
  ElDropdownItem,
  ElMenu,
  ElSubMenu,
  ElMenuItem,
} from "element-plus";
import { SvgIcon } from "@tg-fe/ui";

interface Item {
  name: string;
  iconName: string;
  hidden?: boolean;
  disabled?: boolean;
  children?: Item[];
  onClick?: () => void;
}
interface Props {
  list: Item[];
  className?: {
    outer: string;
    item: string;
  };
  max?: number;
}
const { list, className, max = 3 } = defineProps<Props>();

const spacer = h(ElDivider, { direction: "vertical", class: "h-3 mx-0 !border-[var(--line-line-2)] !leading-0" });

// 计算显示的按钮和更多按钮中的项目
const visibleItems = computed(() => list.slice(0, max));
const moreItems = computed(() => list.slice(max));
const hasMoreItems = computed(() => moreItems.value.length > 0);
</script>

<template>
  <div class="item-center flex gap-[2px]">
    <ElSpace
      :class="['rounded-[12px]', className?.outer || '', { 'rounded-br-[2px] rounded-tr-[2px]': hasMoreItems }]"
      :size="0"
      :spacer="spacer"
    >
      <!-- 显示前 max 个按钮 -->
      <div v-for="item in visibleItems" :key="item.name" @click="item.onClick">
        <ElDropdown popper-class="border-none rounded-[12px] !shadow-[0_3px_12px_0_rgba(0,8,24,0.12)] mt-[-8px]">
          <div
            :class="`flex h-9 shrink-0 cursor-pointer items-center gap-[6px] px-4 text-[14px]/[1.5] outline-none ${className?.item}`"
          >
            <SvgIcon class="h-[18px] w-[18px]" :name="item.iconName"></SvgIcon>
            <div>{{ item.name }}</div>
          </div>
          <template #dropdown>
            <ElDropdownMenu v-if="item.children">
              <ElDropdownItem
                v-for="child in item.children"
                :class="`flex items-center ${className?.item}`"
                :key="child.name"
                @click="child.onClick"
              >
                <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="child.iconName"></SvgIcon>
                <div>{{ child.name }}</div>
              </ElDropdownItem>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
      </div>
    </ElSpace>
    <ElMenu
      class="multi-upload-custom-menu"
      v-if="hasMoreItems"
      mode="horizontal"
      :ellipsis="false"
      popper-class="multi-upload-custom-menu-popper"
    >
      <ElSubMenu index="more">
        <template #title>
          <div
            :class="[
              'flex h-9 shrink-0 cursor-pointer items-center gap-[6px] rounded-[12px] rounded-bl-[2px] rounded-tl-[2px] px-2 text-[14px]/[1.5] outline-none',
              className?.outer || '',
            ]"
          >
            <SvgIcon class="h-[18px] w-[18px]" name="ic_add"></SvgIcon>
          </div>
        </template>
        <template v-for="(item, index) in moreItems" :key="item.name">
          <ElMenuItem v-if="!item.children" :index="`more-${index}`" @click="item.onClick">
            <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="item.iconName"></SvgIcon>
            <div>{{ item.name }}</div>
          </ElMenuItem>
          <ElSubMenu v-else :index="`more-${index}`">
            <template #title>
              <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="item.iconName"></SvgIcon>
              <div>{{ item.name }}</div>
            </template>
            <ElMenuItem
              v-for="(subItem, subIndex) in item.children"
              :key="subItem.name"
              :index="`more-${index}-${subIndex}`"
              @click="subItem.onClick"
            >
              <SvgIcon class="mr-2 h-[16px] w-[16px]" :name="subItem.iconName"></SvgIcon>
              <div>{{ subItem.name }}</div>
            </ElMenuItem>
          </ElSubMenu>
        </template>
      </ElSubMenu>
    </ElMenu>
  </div>
</template>

<style lang="scss" scoped>
.el-space--horizontal {
  :deep(span) {
    line-height: 0 !important;
  }
}
</style>
<style lang="scss">
.multi-upload-custom-menu {
  border: none !important;
  height: auto;
  & > li > .el-sub-menu__title {
    padding: 0 !important;
    border: none !important;
  }
  & > li > .el-sub-menu__title > i {
    display: none;
  }
}
.multi-upload-custom-menu-popper {
  border: none !important;
  .el-menu--horizontal > .el-menu--popup {
    border-radius: 12px;
    li {
      margin: 0 6px;
    }
    li:hover,
    li > .el-sub-menu__title:hover {
      background: var(--fill-fill-3-hover);
      border-radius: 10px;
    }
    .el-popper {
      margin-left: 4px;
    }
  }
  .el-menu--popup {
    min-width: auto;
  }
}
</style>
