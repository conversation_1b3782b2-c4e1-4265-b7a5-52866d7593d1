<script lang="ts" setup>
import { DocInput, SvgIcon } from "@tg-fe/ui";
import { ref } from "vue";
import { ElButton } from "element-plus";
import UploadButton from "../../other/UploadButton.vue";
import { useUploadText } from "@/components/Upload/MultiUpload/components/tab/TextPaste/hooks/useUploadText";

interface TextItem {
  id: number;
  value: string;
}

let nextId = 1;

const list = ref<TextItem[]>([{ id: nextId++, value: "" }]);
const { uploadText } = useUploadText();

const handleAddText = () => {
  list.value.push({ id: nextId++, value: "" });
};

const handleRemoveText = (id: number) => {
  if (list.value.length > 1) {
    list.value = list.value.filter((item) => item.id !== id);
  }
};

const handleOk = () => {
  const filterList = list.value.filter((item) => item.value.trim() !== "").map((item) => item.value);
  uploadText(filterList);
};
</script>

<template>
  <div>
    <div class="h-[428px] space-y-3 overflow-y-auto">
      <div class="text-text-icon-text-3 text-xs">请在此处粘贴文本</div>
      <div class="flex items-center gap-4" v-for="item in list" :key="item.id">
        <DocInput
          class="flex-1"
          v-model="item.value"
          type="textarea"
          placeholder="粘贴文本"
          :rows="list.length > 1 ? 2 : 16"
        />
        <SvgIcon
          class="fill-text-icon-text-4 hover:fill-text-icon-text-3 h-[20px] w-[20px] cursor-pointer"
          v-if="list.length > 1"
          name="ic_add"
          @click="handleRemoveText(item.id)"
        ></SvgIcon>
      </div>

      <UploadButton
        :disabled="list.length >= 50"
        :tooltip="{ content: '最多添加50条文本', disabled: list.length < 50 }"
        @click="handleAddText"
      >
        添加文本
      </UploadButton>
    </div>
    <div class="flex justify-end pt-6">
      <ElButton color="black" @click="handleOk">确定</ElButton>
    </div>
  </div>
</template>

<style scoped>
::-webkit-scrollbar {
  width: 3px; /* 滚动条宽度 */
  border-radius: 31px;
}
::-webkit-scrollbar-thumb {
  background: var(--fill-fill-1, #d4d6dc);
}
</style>
