<script lang="ts" setup>
import UploadKnowledge from "../../upload/UploadKnowledge/index.vue";
import { useTemplateRef } from "vue";
import { useUploadStore, WaitFileInfo } from "@/components/Upload/MultiUpload/hooks/useUploadStore";
import { UploadType } from "@/components/Upload/types";
import { useProjectDetailStore } from "@/store/projectDetail";

const from = "home";
const uploadFrom = "project_upload";

const { uploadKnowledge, getSourceList } = useProjectDetailStore();
const { knowledgeSearchRes, sourcesList } = storeToRefs(useProjectDetailStore());
const knowledgeSearchRef = useTemplateRef("knowledgeSearch");

const flattenTree = (nodes) => {
  const result: any[] = [];
  const traverse = (nodeList) => {
    nodeList.forEach((node) => {
      result.push(node);
      if (node.children) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return result;
};
const handleClick = () => {
  const list = flattenTree(knowledgeSearchRef.value?.searchResult);
  const selectedList = list.filter((item) => item.checked);

  console.log(selectedList, 123);
};
</script>

<template>
  <div>
    <UploadKnowledge class="!h-[428px] overflow-y-auto" ref="knowledgeSearch" knowsearch_from="" />
    <div class="flex justify-end pt-6">
      <ElButton color="black" @click="handleClick">确定</ElButton>
    </div>
  </div>
</template>
