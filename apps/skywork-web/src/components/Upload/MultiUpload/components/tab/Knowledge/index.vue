<script lang="ts" setup>
import UploadKnowledge from "../../upload/UploadKnowledge/index.vue";
import { useTemplateRef, inject } from "vue";
import type { KnowledgeSelectedItem } from "@/components/Upload/MultiUploadWithDialog/store/useMultiUploadDialogStore";

const knowledgeSearchRef = useTemplateRef("knowledgeSearch");

// 注入弹窗 store
const dialogStore = inject("multiUploadDialogStore") as any;

const flattenTree = (nodes: any) => {
  const result: any[] = [];
  const traverse = (nodeList: any) => {
    nodeList.forEach((node: any) => {
      result.push(node);
      if (node.children) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return result;
};
const handleClick = () => {
  const list = flattenTree(knowledgeSearchRef.value?.searchResult);
  const selectedList = list.filter((item) => item.checked);

  // 转换为标准格式并保存到 store
  const formattedList: KnowledgeSelectedItem[] = selectedList.map((item) => ({
    id: item.space?.file_id || item.id,
    name: item.space?.file_name || item.name,
    checked: item.checked,
    ...item, // 保留原始数据
  }));

  // 保存到弹窗 store
  if (dialogStore) {
    dialogStore.setSelectedList(formattedList);
    console.log("已保存选中的知识库列表到 store:", formattedList);
  } else {
    console.log("未找到弹窗 store，直接输出:", formattedList);
  }
};
</script>

<template>
  <div>
    <UploadKnowledge class="!h-[428px] overflow-y-auto" ref="knowledgeSearch" knowsearch_from="" />
    <div class="flex justify-end pt-6">
      <ElButton color="black" @click="handleClick">确定</ElButton>
    </div>
  </div>
</template>
