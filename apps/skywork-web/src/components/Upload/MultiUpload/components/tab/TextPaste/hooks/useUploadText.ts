import { MAX_TEXT_LENGTH } from "@/components/Upload/const";
import { useUploadStore, WaitFileInfo } from "@/components/Upload/MultiUpload/hooks/useUploadStore";
import { UploadType } from "@/components/Upload/types";
import { $t } from "@tg-fe/i18n";
import { DocMessage } from "@tg-fe/ui";

export const useUploadText = () => {
  const from = "home";
  const uploadFrom = "project_upload";
  const type = UploadType.CustomText;

  const { addFilesToWaitUpload, uploadFiles, clearWaitUploadList } = useUploadStore(from);
  const verifyText = (textString: string) => {
    let text = textString.trim();
    if (!text) {
      return $t("common.validText");
    }
    if (text.length > MAX_TEXT_LENGTH) {
      return $t("common.pasteText.limit1");
    }
    return "";
  };

  const handleLinkTextConfirm = async (text: string, index: number) => {
    let file: Blob | null = null;
    let info: WaitFileInfo["info"] | null = null;
    const errText = verifyText(text);
    if (errText) {
      DocMessage.warning(errText);
      return;
    }
    file = new Blob([text], { type: "text/plain" });
    info = {
      id: index,
      knowledge_type: 1,
      file_name: text.replace(/[\n\r]/g, " ").slice(0, 10),
      file_type: "txt",
      file_size: file.size,
    };

    addFilesToWaitUpload([
      { selected: true, info, file, trackData: { file_from: type, upload_from: uploadFrom } },
    ] as WaitFileInfo[]);
  };

  // 将待上传文件，提交上传
  const submit = async () => {
    try {
      const pageSourceObj = uploadFrom === "project_upload" ? { page_source: 6 } : {};
      await uploadFiles(pageSourceObj);
    } catch (error) {
      clearWaitUploadList();
    }
  };

  const uploadText = (list: string[]) => {
    list.some((item) => {
      const errText = verifyText(item);
      if (errText !== "") {
        DocMessage.warning(errText);
        return true;
      }
      return false;
    });

    list.forEach((item, index) => {
      handleLinkTextConfirm(item, index);
    });

    submit();
  };

  return { uploadText };
};
